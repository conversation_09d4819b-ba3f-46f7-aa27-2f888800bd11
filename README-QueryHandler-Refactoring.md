# QueryHandler Refactoring Scripts

Bộ script PowerShell để refactor các <PERSON><PERSON><PERSON><PERSON> trong Webaby.Core project từ synchronous sang asynchronous pattern với dependency injection và AutoMapper.

## Các <PERSON>ript <PERSON> Gồm

### 1. `run-complete-refactoring.ps1` (<PERSON><PERSON><PERSON> chính)
Script master để chạy toàn bộ quá trình refactoring.

**<PERSON><PERSON>ch sử dụng:**
```powershell
# Chạy với tham số mặc định
.\run-complete-refactoring.ps1

# Chạy với tham số tùy chỉnh
.\run-complete-refactoring.ps1 -RootPath "C:\Your\Path\Webaby.Core" -Modules @('Access','BusinessSettings') -SkipBuild

# Chạy với WhatIf để xem preview
.\run-complete-refactoring.ps1 -WhatIf
```

### 2. `refactor-queryhandlers-improved.ps1`
Script chính để refactor các QueryHandler files.

### 3. `create-missing-entities.ps1`
Script để tạo các <PERSON>tity classes và Data classes còn thiếu.

## <PERSON><PERSON>c <PERSON>hay <PERSON>c <PERSON>hực <PERSON>n

### 1. Thêm Using Statements
```csharp
using System.Threading.Tasks;
using AutoMapper;
using Webaby.Data;
using Webaby.Localization;
using Webaby;
using Microsoft.EntityFrameworkCore;
```

### 2. Thêm Constructor với Dependency Injection
```csharp
public GetAllAccessQueryHandler(
    IEntitySet entitySet,
    IRepository repository,
    IText text,
    IMapper mapper)
  : base(entitySet, repository, text, mapper)
{ }
```

### 3. Chuyển Đổi Method Signature
**Trước:**
```csharp
public override QueryResult<AccessData> Execute(GetAllAccessQuery query)
```

**Sau:**
```csharp
public override async Task<QueryResult<AccessData>> ExecuteAsync(GetAllAccessQuery query)
```

### 4. Chuyển Đổi EntitySet Calls
**Trước:**
```csharp
var entity = EntitySet.Get<AccessEntity>();
```

**Sau:**
```csharp
var entity = await EntitySet.GetAsync<AccessEntity>();
```

### 5. Chuyển Đổi LINQ Queries với Where
**Trước:**
```csharp
var data = EntitySet.Get<AccessEntity>().Where(x => x.Id == query.Id);
return new QueryResult<AccessData>(data);
```

**Sau:**
```csharp
var queryable = EntitySet.Get<AccessEntity>().Where(x => x.Id == query.Id);
var entities = await queryable.ToArrayAsync();
return new QueryResult<AccessData>(entities.Select(x => Mapper.Map<AccessData>(x)));
```

### 6. Chuyển Đổi Mapping
**Trước:**
```csharp
return new QueryResult<AccessData>(AccessData.FromEntity(entity));
```

**Sau:**
```csharp
return new QueryResult<AccessData>(Mapper.Map<AccessData>(entity));
```

## Các File Được Tạo Tự Động

### Entity Classes
- `BusinessPermissionEntity.cs`
- `RoleBusinessPermissionEntity.cs`

### Data Classes
- `BusinessPermissionData.cs`
- `RoleBusinessPermissionData.cs`

### Query Classes
- `GetMenuTreeItemQuery.cs`
- `MenuTreeItem.cs`
- `ITreeNode.cs`

## Các Lỗi Thường Gặp và Cách Sửa

### 1. Missing Entity Classes
Script sẽ tự động tạo các Entity classes cơ bản. Nếu cần thêm properties, hãy chỉnh sửa thủ công.

### 2. HttpVerbs vs HttpMethods
Script tự động thay thế `HttpVerbs` bằng `HttpMethods`.

### 3. System.Web.Mvc References
Script tự động loại bỏ các using statements không cần thiết.

### 4. Container References
Script tự động thêm `this.` prefix cho Container calls.

## Kiểm Tra Sau Khi Refactor

1. **Build Project:**
   ```bash
   dotnet build Webaby.Core/Webaby.Core.csproj
   ```

2. **Kiểm tra các file đã được thêm vào .csproj**

3. **Test các QueryHandler để đảm bảo hoạt động đúng**

## Lưu Ý Quan Trọng

- **Backup code** trước khi chạy script
- Script sử dụng `-WhatIf` parameter để preview changes
- Tất cả changes được log ra console với màu sắc
- Script tự động cập nhật file .csproj
- Nếu có lỗi build, hãy review và fix thủ công

## Troubleshooting

### Lỗi Execution Policy
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### Lỗi Missing Dependencies
Đảm bảo các NuGet packages sau đã được install:
- Microsoft.EntityFrameworkCore
- AutoMapper
- Webaby.Data
- Webaby.Localization

### Lỗi Build
Kiểm tra:
1. Tất cả using statements đã đúng
2. Các Entity classes đã được tạo
3. Namespace declarations đã đúng
4. Method signatures đã được update đúng

## Liên Hệ

Nếu gặp vấn đề, hãy kiểm tra:
1. Console output để xem chi tiết lỗi
2. Build output để xem compilation errors
3. File changes để đảm bảo syntax đúng
