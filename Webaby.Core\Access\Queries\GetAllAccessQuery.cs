﻿namespace Webaby.Core.Access.Queries
{
    public class GetAllAccessQuery : QueryBase<AccessData>
    {
        
    }

    internal class GetAllAccessQueryHandler : QueryHandlerBase<GetAllAccessQuery, AccessData>
    {
        public override QueryResult<AccessData> Execute(GetAllAccessQuery query)
        {
            var entity = EntitySet.Get<AccessEntity>();
            return QueryResult.Create(entity, AccessData.FromEntity);
        }
    }
}
