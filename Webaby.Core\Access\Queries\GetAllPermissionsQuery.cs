﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Webaby.Core.Access.Queries
{
    public class GetAllPermissionsQuery : QueryBase<BusinessPermissionData>
    {
        public bool IncludeDeleted { get; set; }
    }

    internal class GetAllPermissionsQueryHandler :
        QueryHandlerBase<GetAllPermissionsQuery, BusinessPermissionData>
    {
        public override QueryResult<BusinessPermissionData> Execute(GetAllPermissionsQuery query)
        {
            var permissionEntities = EntitySet.Get<BusinessPermissionEntity>(query.IncludeDeleted);
            return QueryResult.Create(permissionEntities, BusinessPermissionData.FromEntity);
        }
    }
}