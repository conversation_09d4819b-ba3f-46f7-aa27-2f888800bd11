﻿using System.Web.Mvc;
using System.Linq;

namespace Webaby.Core.Access.Queries
{
    public class GetApiAccessQuery : QueryBase<AccessData>
    {
        public string UrlPart { get; set; }

        public HttpVerbs Method { get; set; }
    }

    internal class GetApiAccessQueryHandler : QueryHandlerBase<GetApiAccessQuery, AccessData>
    {
        public override QueryResult<AccessData> Execute(GetApiAccessQuery query)
        {
            var entity = from access in EntitySet.Get<AccessEntity>()
                         where access.ActionName == query.UrlPart && access.Method == query.Method
                         select access;
            return QueryResult.Create(entity, AccessData.FromEntity);
        }
    }
}
