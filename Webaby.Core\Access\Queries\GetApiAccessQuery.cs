﻿using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Webaby.Data;
using Webaby.Localization;
using Webaby;
using Microsoft.EntityFrameworkCore;

namespace Webaby.Core.Access.Queries
{
    public class GetApiAccessQuery : QueryBase<AccessData>
    {
        public string UrlPart { get; set; }

        public HttpMethods Method { get; set; }
    }

    internal class GetApiAccessQueryHandler : QueryHandlerBase<GetApiAccessQuery, AccessData>
    {
        public GetApiAccessQueryHandler(
            IEntitySet entitySet,
            IRepository repository,
            IText text,
            IMapper mapper)
          : base(entitySet, repository, text, mapper)
        { }

        public override async Task<QueryResult<AccessData>> ExecuteAsync(GetApiAccessQuery query)
        {
            var queryable = EntitySet.Get<AccessEntity>().Where(access => access.ActionName == query.UrlPart && access.Method == query.Method);
            var entities = await queryable.ToArrayAsync();
            return new QueryResult<AccessData>(entities.Select(x => Mapper.Map<AccessData>(x)));
        }
    }
}

