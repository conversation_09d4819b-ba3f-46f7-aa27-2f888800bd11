﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Webaby.Caching;
using Webaby.Web;
using AutoMapper;
using Webaby.Data;
using Webaby.Localization;
using Webaby;

namespace Webaby.Core.Access.Queries
{
    public class GetMenuTreeItemQueryHandler : QueryHandlerBase<GetMenuTreeItemQuery, MenuTreeItem>
    {
        public GetMenuTreeItemQueryHandler(
            IEntitySet entitySet,
            IRepository repository,
            IText text,
            IMapper mapper)
          : base(entitySet, repository, text, mapper)
        { }

        public override async Task<QueryResult<MenuTreeItem>> ExecuteAsync(GetMenuTreeItemQuery query)
        {
            IEnumerable<ITreeNode> menuData = Container.One<ICacheProvider>().Get("list_menu_item") as IEnumerable<ITreeNode>;
            if (menuData == null)
            {
                var queryable = EntitySet.Get<MenuItemEntity>().Where(x => x.DisplayOrder >= 0);
                var menuItems = await queryable.ToArrayAsync();
                menuData = menuItems.Select(x => new MenuItemData
                {
                    ActionName = x.ActionName,
                    BusinessPermissionId = x.BusinessPermissionId,
                    ControllerName = x.ControllerName,
                    DisplayOrder = x.DisplayOrder,
                    Id = x.Id,
                    IsSection = x.IsSection,
                    MenuClass = x.MenuClass,
                    ParentId = x.ParentId,
                    Title = x.Title,
                } as ITreeNode);

                if (menuData.Any())
                {
                    Container.One<ICacheProvider>().Set("list_menu_item", menuData, 0);
                }
            }
            var tree = MenuTreeItem.ParseFromList(menuData.ToList(), null);
            return QueryResult.Create(tree);
        }
    }
}

