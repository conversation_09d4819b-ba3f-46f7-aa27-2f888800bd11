using System;

namespace Webaby.Core.Access.Queries
{
    public interface ITreeNode
    {
        Guid Id { get; set; }
        Guid? BusinessPermissionId { get; set; }
        Guid? ParentId { get; set; }
        string Title { get; set; }
        string ActionName { get; set; }
        string ControllerName { get; set; }
        string MenuClass { get; set; }
        bool IsSection { get; set; }
        int DisplayOrder { get; set; }
    }
}
