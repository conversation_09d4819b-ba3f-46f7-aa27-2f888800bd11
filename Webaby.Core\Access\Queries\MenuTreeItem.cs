using System;
using System.Collections.Generic;
using System.Linq;

namespace Webaby.Core.Access.Queries
{
    public class MenuTreeItem
    {
        public Guid Id { get; set; }
        public Guid? BusinessPermissionId { get; set; }
        public Guid? ParentId { get; set; }
        public string Title { get; set; }
        public string ActionName { get; set; }
        public string ControllerName { get; set; }
        public string MenuClass { get; set; }
        public bool IsSection { get; set; }
        public int DisplayOrder { get; set; }
        public List<MenuTreeItem> Children { get; set; } = new List<MenuTreeItem>();

        public static List<MenuTreeItem> ParseFromList(List<ITreeNode> menuData, Guid? parentId)
        {
            var result = new List<MenuTreeItem>();

            var items = menuData.Where(x => x.ParentId == parentId)
                               .OrderBy(x => x.DisplayOrder)
                               .ToList();

            foreach (var item in items)
            {
                var menuItem = new MenuTreeItem
                {
                    Id = item.Id,
                    BusinessPermissionId = item.BusinessPermissionId,
                    ParentId = item.ParentId,
                    Title = item.Title,
                    ActionName = item.ActionName,
                    ControllerName = item.ControllerName,
                    MenuClass = item.MenuClass,
                    IsSection = item.IsSection,
                    DisplayOrder = item.DisplayOrder,
                    Children = ParseFromList(menuData, item.Id)
                };

                result.Add(menuItem);
            }

            return result;
        }
    }
}
