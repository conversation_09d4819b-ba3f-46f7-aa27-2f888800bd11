﻿using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using Webaby.Caching;

namespace Webaby.Core.BusinessSettings.Queries
{
    public class GetAllBusinessSettingsQuery : QueryBase<BusinessSettingData>
    {

    }

    internal class GetAllBusinessSettingsQueryHandler : QueryHandlerBase<GetAllBusinessSettingsQuery, BusinessSettingData>
    {
        public override QueryResult<BusinessSettingData> Execute(GetAllBusinessSettingsQuery query)
        {
            IEnumerable<BusinessSettingData> businessSettingData =
                Container.One<ICacheProvider>().Get("business_settings") as IEnumerable<BusinessSettingData>;
            if (businessSettingData == null)
            {
                SqlCommand cmd = new SqlCommand
                {
                    CommandType = CommandType.Text,
                    CommandText = "select * from dbo.BusinessSettings order by [Order] asc"
                };
                businessSettingData = EntitySet.ExecuteReadCommand<BusinessSettingData>(cmd);
                if (businessSettingData.Any())
                {
                    Container.One<ICacheProvider>().Set("business_settings", businessSettingData, 0);
                }
            }
            return new QueryResult<BusinessSettingData>(businessSettingData);
        }
    }
}
