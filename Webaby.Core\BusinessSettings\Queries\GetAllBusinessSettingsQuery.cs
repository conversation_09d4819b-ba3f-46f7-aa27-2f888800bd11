﻿using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using Webaby.Caching;
using System.Threading.Tasks;
using AutoMapper;
using Webaby.Data;
using Webaby.Localization;
using Webaby;
using Webaby.BusinessSetting.Queries;

namespace Webaby.Core.BusinessSettings.Queries
{
    public class GetAllBusinessSettingsQuery : QueryBase<BusinessSettingData>
    {

    }

    internal class GetAllBusinessSettingsQueryHandler : QueryHandlerBase<GetAllBusinessSettingsQuery, BusinessSettingData>
    {
        public GetAllBusinessSettingsQueryHandler(
            IEntitySet entitySet,
            IRepository repository,
            IText text,
            IMapper mapper)
          : base(entitySet, repository, text, mapper)
        { }

        public override async Task<QueryResult<BusinessSettingData>> ExecuteAsync(GetAllBusinessSettingsQuery query)
        {
            IEnumerable<BusinessSettingData> businessSettingData =
                Container.One<ICacheProvider>().Get("business_settings") as IEnumerable<BusinessSettingData>;
            if (businessSettingData == null)
            {
                SqlCommand cmd = new SqlCommand
                {
                    CommandType = CommandType.Text,
                    CommandText = "select * from dbo.BusinessSettings order by [Order] asc"
                };
                businessSettingData = await EntitySet.ExecuteReadCommandAsync<BusinessSettingData>(cmd);
                if (businessSettingData.Any())
                {
                    Container.One<ICacheProvider>().Set("business_settings", businessSettingData, 0);
                }
            }
            return new QueryResult<BusinessSettingData>(businessSettingData);
        }
    }
}
