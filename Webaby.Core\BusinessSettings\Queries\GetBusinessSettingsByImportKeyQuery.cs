﻿using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using Webaby.Caching;
using System.Threading.Tasks;
using AutoMapper;
using Webaby.Data;
using Webaby.Localization;
using Webaby;
using Webaby.BusinessSetting;
using Microsoft.EntityFrameworkCore;

namespace Webaby.Core.BusinessSettings.Queries
{
    public class GetBusinessSettingsByImportKeyQuery : QueryBase<BusinessSettingEntity>
    {
        public string ImportKey { get; set; }
    }

    internal class GetBusinessSettingsByImportKeyQueryHandler : QueryHandlerBase<GetBusinessSettingsByImportKeyQuery, BusinessSettingEntity>
    {
        public GetBusinessSettingsByImportKeyQueryHandler(
            IEntitySet entitySet,
            IRepository repository,
            IText text,
            IMapper mapper)
          : base(entitySet, repository, text, mapper)
        { }

        public override async Task<QueryResult<BusinessSettingEntity>> ExecuteAsync(GetBusinessSettingsByImportKeyQuery query)
        {
            var queryable = EntitySet.Get<BusinessSettingEntity>().Where(x => x.ImportKey == query.ImportKey);
            var entities = await queryable.ToArrayAsync();
            return new QueryResult<BusinessSettingEntity>(entities);
        }
    }
}
