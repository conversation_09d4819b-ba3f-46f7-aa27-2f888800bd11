# 
# .SYNOPSIS 
#   Tạo các Entity classes còn thiếu cho QueryHandler refactoring
# .DESCRIPTION 
#   Script này tạo các Entity classes và Data classes còn thiếu
#   dựa trên các lỗi compilation thường gặp
# 
[CmdletBinding(SupportsShouldProcess=$true)] 
param( 
  [string] $RootPath = 'C:\Users\<USER>\source\Workspaces\CEP_NETCORE\Webaby.Core'
) 

$createdFiles = @()

# Tạo BusinessPermissionEntity nếu chưa có
$businessPermissionEntityPath = Join-Path $RootPath "Access\BusinessPermissionEntity.cs"
if (-not (Test-Path $businessPermissionEntityPath)) {
    $businessPermissionEntityContent = @'
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Webaby.Data;

namespace Webaby.Core.Access
{
    [Table("BusinessPermission", Schema = "dbo")]
    public class BusinessPermissionEntity : IEntity
    {
        [Key]
        public Guid Id { get; set; }

        [Column]
        public string Name { get; set; } = string.Empty;

        [Column]
        public string Code { get; set; } = string.Empty;

        [Column]
        public Guid? ParentId { get; set; }

        [Column]
        public int Order { get; set; }

        [Column]
        public bool Deleted { get; set; }

        [Column]
        public DateTime? DeletedDate { get; set; }

        [Column]
        public Guid? DeletedBy { get; set; }
    }
}
'@
    Set-Content -Path $businessPermissionEntityPath -Value $businessPermissionEntityContent -Encoding UTF8
    $createdFiles += $businessPermissionEntityPath
    Write-Host "✓ Created BusinessPermissionEntity.cs" -ForegroundColor Green
}

# Tạo RoleBusinessPermissionEntity nếu chưa có
$roleBusinessPermissionEntityPath = Join-Path $RootPath "Access\RoleBusinessPermissionEntity.cs"
if (-not (Test-Path $roleBusinessPermissionEntityPath)) {
    $roleBusinessPermissionEntityContent = @'
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Webaby.Data;

namespace Webaby.Core.Access
{
    [Table("RoleBusinessPermission", Schema = "dbo")]
    public class RoleBusinessPermissionEntity : IEntity
    {
        [Key]
        public Guid Id { get; set; }

        [Column]
        public Guid RoleId { get; set; }

        [Column]
        public Guid BusinessPermissionId { get; set; }

        [Column]
        public bool Granted { get; set; }
    }
}
'@
    Set-Content -Path $roleBusinessPermissionEntityPath -Value $roleBusinessPermissionEntityContent -Encoding UTF8
    $createdFiles += $roleBusinessPermissionEntityPath
    Write-Host "✓ Created RoleBusinessPermissionEntity.cs" -ForegroundColor Green
}

# Tạo BusinessPermissionData nếu chưa có
$businessPermissionDataPath = Join-Path $RootPath "Access\Queries\BusinessPermissionData.cs"
if (-not (Test-Path $businessPermissionDataPath)) {
    $businessPermissionDataContent = @'
using System;

namespace Webaby.Core.Access.Queries
{
    public class BusinessPermissionData
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public Guid? ParentId { get; set; }
        public int Order { get; set; }
        public bool Deleted { get; set; }
        public DateTime? DeletedDate { get; set; }
        public Guid? DeletedBy { get; set; }
    }
}
'@
    Set-Content -Path $businessPermissionDataPath -Value $businessPermissionDataContent -Encoding UTF8
    $createdFiles += $businessPermissionDataPath
    Write-Host "✓ Created BusinessPermissionData.cs" -ForegroundColor Green
}

# Tạo RoleBusinessPermissionData nếu chưa có
$roleBusinessPermissionDataPath = Join-Path $RootPath "Access\Queries\RoleBusinessPermissionData.cs"
if (-not (Test-Path $roleBusinessPermissionDataPath)) {
    $roleBusinessPermissionDataContent = @'
using System;

namespace Webaby.Core.Access.Queries
{
    public class RoleBusinessPermissionData
    {
        public Guid Id { get; set; }
        public Guid RoleId { get; set; }
        public Guid BusinessPermissionId { get; set; }
        public bool Granted { get; set; }
    }
}
'@
    Set-Content -Path $roleBusinessPermissionDataPath -Value $roleBusinessPermissionDataContent -Encoding UTF8
    $createdFiles += $roleBusinessPermissionDataPath
    Write-Host "✓ Created RoleBusinessPermissionData.cs" -ForegroundColor Green
}

# Cập nhật .csproj với các file mới tạo
$CsProjFile = Join-Path $RootPath "Webaby.Core.csproj"
if ($createdFiles.Count -gt 0 -and $PSCmdlet.ShouldProcess($CsProjFile,'Add created entities to .csproj')) {
    try {
        [xml]$proj = Get-Content -Path $CsProjFile 
        $ns = $proj.Project.NamespaceURI 
        $ig = $proj.Project.ItemGroup | Where-Object { $_.Compile } | Select-Object -First 1 
        if (-not $ig) { 
            $ig = $proj.CreateElement('ItemGroup',$ns) 
            $proj.Project.AppendChild($ig) | Out-Null 
        } 
        $base = Split-Path -Path $CsProjFile -Parent 
        $uri = New-Object System.Uri("$base\") 
        
        foreach ($f in $createdFiles) { 
            $rel = $uri.MakeRelativeUri((New-Object System.Uri($f))).ToString() -replace '/', '\' 
            if (-not ($ig.Compile | Where-Object { $_.Include -eq $rel })) { 
                $node = $proj.CreateElement('Compile',$ns) 
                $node.SetAttribute('Include',$rel) 
                $ig.AppendChild($node) | Out-Null 
                Write-Host "   + Added entity file: $rel" -ForegroundColor Cyan
            } 
        } 
        $proj.Save($CsProjFile)
        Write-Host "✓ Updated .csproj with entity files" -ForegroundColor Green
    }
    catch {
        Write-Host "✗ Error adding entity files to .csproj: $_" -ForegroundColor Red
    }
}

Write-Host "`n=== Entity Creation Summary ===" -ForegroundColor Magenta
Write-Host "Created $($createdFiles.Count) entity files" -ForegroundColor White
foreach ($file in $createdFiles) {
    Write-Host "  - $(Split-Path $file -Leaf)" -ForegroundColor Gray
}
Write-Host "Entity creation completed!" -ForegroundColor Green
