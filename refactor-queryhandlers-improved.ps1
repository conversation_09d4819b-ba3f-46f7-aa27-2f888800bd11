#
# .SYNOPSIS
#   Refactor QueryHandler → async + ctor + LINQ-to-async for .Where queries + mapping → update .csproj
# .DESCRIPTION
#   - Với mỗi class *QueryHandler* trong thư mục Queries của modules:
#       1) Thêm thiếu usings
#       2) Chèn constructor
#       3) LINQ-to-async cho Execute/ExecuteAsync có `EntitySet.Get<T>().Where(...)`
#       4) Nếu không match bước 3, thực hiện:
#          a) Đ<PERSON>i signature `Execute` → `async Task<QueryResult<T>> ExecuteAsync(...)`
#          b) Thay `EntitySet.Get<T>(...)` → `await EntitySet.GetAsync<T>(...)`
#          c) Mapping động `new QueryResult<T>(X.FromEntity(...))` & `QueryResult.Create(...)`
#   - Cuối cùng cập nhật `.csproj` với `<Compile Include="…"/>`.
#
[CmdletBinding(SupportsShouldProcess=$true)]
param(
  [string]   $RootPath   = 'C:\Users\<USER>\source\Workspaces\CEP_NETCORE\Webaby.Core',
  [string[]] $Modules    = @('Access','BusinessSettings'),
  [string]   $CsProjFile = 'C:\Users\<USER>\source\Workspaces\CEP_NETCORE\Webaby.Core\Webaby.Core.csproj'
)

$modified = [System.Collections.Generic.List[string]]::new()

foreach ($mod in $Modules) {
  $dir = Join-Path (Join-Path $RootPath $mod) 'Queries'
  if (-not (Test-Path $dir)) {
    Write-Host "Directory not found: $dir" -ForegroundColor Yellow
    continue
  }

  Write-Host "Processing module: $mod" -ForegroundColor Green
  Get-ChildItem $dir -Recurse -Filter '*.cs' | ForEach-Object {
    $file = $_.FullName
    if (-not (Select-String -Path $file -Pattern 'class\s+\w+QueryHandler' -Quiet)) {
      Write-Host "  Skipping non-QueryHandler: $($_.Name)" -ForegroundColor Gray
      return
    }
    if (-not $PSCmdlet.ShouldProcess($file,'Refactor')) { return }

    Write-Host "  Refactoring: $($_.Name)" -ForegroundColor Cyan
    $text = Get-Content -Raw -Path $file

    # 1) Thêm thiếu usings
    $needed = @(
      'using System.Threading.Tasks;',
      'using AutoMapper;',
      'using Webaby.Data;',
      'using Webaby.Localization;',
      'using Webaby;',
      'using Microsoft.EntityFrameworkCore;'
    )

    # Tìm vị trí cuối cùng của using statements
    $lines = $text -split "`r?`n"
    $lastUsingIndex = -1
    for ($i = 0; $i -lt $lines.Length; $i++) {
        if ($lines[$i] -match '^using .+;$') {
            $lastUsingIndex = $i
        }
    }

    if ($lastUsingIndex -ge 0) {
        foreach ($u in $needed) {
            if ($text -notmatch [regex]::Escape($u)) {
                $lines = $lines[0..$lastUsingIndex] + $u + $lines[($lastUsingIndex+1)..($lines.Length-1)]
                $lastUsingIndex++
            }
        }
        $text = $lines -join "`r`n"
    }

    # 2) Chèn constructor nếu chưa có
    if ($text -notmatch 'public\s+\w+QueryHandler\s*\(') {
        $text = $text -replace '(?ms)(internal\s+class\s+(\w+QueryHandler)\s*:\s*[^{]+)\{', @'
$1 {
    public $2(
        IEntitySet entitySet,
        IRepository repository,
        IText text,
        IMapper mapper)
      : base(entitySet, repository, text, mapper)
    { }
'@
    }

    # 3) Xử lý các pattern đặc biệt

    # Pattern 1: LINQ với .Where() - chuyển thành async
    $linqWherePattern = @'
(?ms)public\s+override\s+QueryResult<(\w+)>\s+Execute\s*\([^)]*\)\s*\{[^}]*?var\s+(\w+)\s*=\s*EntitySet\.Get<[^>]+>\(\)\.Where\([^)]+\)[^;]*;[^}]*?return\s+(?:new\s+QueryResult<\1>\(\2\)|QueryResult\.Create\(\2[^)]*\))[^}]*?\}
'@

    $text = [regex]::Replace($text, $linqWherePattern, {
        param($match)
        $returnType = $match.Groups[1].Value
        $varName = $match.Groups[2].Value
        $fullMatch = $match.Value

        # Extract method signature
        if ($fullMatch -match 'public\s+override\s+QueryResult<\w+>\s+Execute\s*\(([^)]*)\)') {
            $params = $matches[1]
            # Extract the LINQ query
            if ($fullMatch -match 'var\s+\w+\s*=\s*(EntitySet\.Get<[^>]+>\(\)\.Where\([^)]+\))') {
                $linqQuery = $matches[1]
                return @"
public override async Task<QueryResult<$returnType>> ExecuteAsync($params)
{
    var queryable = $linqQuery;
    var entities = await queryable.ToArrayAsync();
    return new QueryResult<$returnType>(entities.Select(x => Mapper.Map<$returnType>(x)));
}
"@
            }
        }
        return $match.Value
    })

    # 4) Đổi signature Execute → ExecuteAsync nếu chưa async
    $text = $text -replace 'public override\s+QueryResult<(\w+)>\s+Execute\s*\(([^)]*)\)', 'public override async Task<QueryResult<$1>> ExecuteAsync($2)'

    # 5) Thay EntitySet.Get<T>(...) → await EntitySet.GetAsync<T>(...)
    $text = [regex]::Replace($text, 'EntitySet\.Get<([^>]+)>\(([^)]*)\)', 'await EntitySet.GetAsync<$1>($2)')

    # 6) Thay EntitySet.ExecuteReadCommand → await EntitySet.ExecuteReadCommandAsync
    $text = $text -replace 'EntitySet\.ExecuteReadCommand<([^>]+)>\(([^)]+)\)', 'await EntitySet.ExecuteReadCommandAsync<$1>($2)'

    # 7) Mapping new QueryResult<T>(X.FromEntity(var)) → Mapper.Map
    $text = [regex]::Replace($text, 'new\s+QueryResult<(\w+)>\(\s*(\w+)\.FromEntity\(\s*(\w+)\s*\)\s*\)', 'new QueryResult<$1>(Mapper.Map<$1>($3))')

    # 8) Mapping QueryResult.Create(entity, X.FromEntity) → Mapper.Map
    $text = [regex]::Replace($text, 'QueryResult\.Create\(\s*(\w+)\s*,\s*(\w+)\.FromEntity\s*\)', 'QueryResult.Create($1, x => Mapper.Map<$2>(x))')

    # 9) Sửa Container.One<ICacheProvider>() thành this.Container.One<ICacheProvider>() nếu có
    $text = $text -replace '\bContainer\.One<', 'this.Container.One<'

    # 10) Sửa các lỗi syntax thường gặp
    $text = $text -replace '\)\)\);', '));'  # Loại bỏ dấu ngoặc thừa
    $text = $text -replace 'using System\.Web\.Mvc;', ''  # Loại bỏ System.Web.Mvc

    # 11) Thay HttpVerbs bằng HttpMethods
    $text = $text -replace '\bHttpVerbs\b', 'HttpMethods'

    # Lưu file
    try {
        Set-Content -Path $file -Value $text -Encoding UTF8
        $modified.Add($file)
        Write-Host "    ✓ Successfully refactored" -ForegroundColor Green
    }
    catch {
        Write-Host "    ✗ Error saving file: $_" -ForegroundColor Red
    }
  }
}

# 12) Cập nhật .csproj
if ($modified.Count -gt 0 -and $PSCmdlet.ShouldProcess($CsProjFile,'Update .csproj')) {
  Write-Host "`nUpdating csproj: $CsProjFile" -ForegroundColor Green
  try {
    [xml]$proj = Get-Content -Path $CsProjFile
    $ns = $proj.Project.NamespaceURI
    $ig = $proj.Project.ItemGroup | Where-Object { $_.Compile } | Select-Object -First 1
    if (-not $ig) {
      $ig = $proj.CreateElement('ItemGroup',$ns)
      $proj.Project.AppendChild($ig) | Out-Null
    }
    $base = Split-Path -Path $CsProjFile -Parent
    $uri = New-Object System.Uri("$base\")

    $addedCount = 0
    foreach ($f in $modified) {
      $rel = $uri.MakeRelativeUri((New-Object System.Uri($f))).ToString() -replace '/', '\'
      if (-not ($ig.Compile | Where-Object { $_.Include -eq $rel })) {
        if ($PSCmdlet.ShouldProcess($rel,'Add Compile Include')) {
          $node = $proj.CreateElement('Compile',$ns)
          $node.SetAttribute('Include',$rel)
          $ig.AppendChild($node) | Out-Null
          Write-Host "   + Added Compile Include='$rel'" -ForegroundColor Cyan
          $addedCount++
        }
      }
    }

    if ($addedCount -gt 0) {
        $proj.Save($CsProjFile)
        Write-Host "✓ Updated .csproj with $addedCount new files" -ForegroundColor Green
    } else {
        Write-Host "✓ All files already in .csproj" -ForegroundColor Yellow
    }
  }
  catch {
    Write-Host "✗ Error updating .csproj: $_" -ForegroundColor Red
  }
}
else {
  Write-Host 'No changes made.' -ForegroundColor Yellow
}

# 13) Tạo các file còn thiếu nếu cần
$missingFiles = @()

# Kiểm tra và tạo ITreeNode.cs
$iTreeNodePath = Join-Path $RootPath "Access\Queries\ITreeNode.cs"
if (-not (Test-Path $iTreeNodePath)) {
    $iTreeNodeContent = @'
using System;

namespace Webaby.Core.Access.Queries
{
    public interface ITreeNode
    {
        Guid Id { get; set; }
        Guid? BusinessPermissionId { get; set; }
        Guid? ParentId { get; set; }
        string Title { get; set; }
        string ActionName { get; set; }
        string ControllerName { get; set; }
        string MenuClass { get; set; }
        bool IsSection { get; set; }
        int DisplayOrder { get; set; }
    }
}
'@
    Set-Content -Path $iTreeNodePath -Value $iTreeNodeContent -Encoding UTF8
    $missingFiles += $iTreeNodePath
    Write-Host "✓ Created ITreeNode.cs" -ForegroundColor Green
}

# Kiểm tra và tạo MenuTreeItem.cs
$menuTreeItemPath = Join-Path $RootPath "Access\Queries\MenuTreeItem.cs"
if (-not (Test-Path $menuTreeItemPath)) {
    $menuTreeItemContent = @'
using System;
using System.Collections.Generic;
using System.Linq;

namespace Webaby.Core.Access.Queries
{
    public class MenuTreeItem
    {
        public Guid Id { get; set; }
        public Guid? BusinessPermissionId { get; set; }
        public Guid? ParentId { get; set; }
        public string Title { get; set; } = string.Empty;
        public string ActionName { get; set; } = string.Empty;
        public string ControllerName { get; set; } = string.Empty;
        public string MenuClass { get; set; } = string.Empty;
        public bool IsSection { get; set; }
        public int DisplayOrder { get; set; }
        public List<MenuTreeItem> Children { get; set; } = new List<MenuTreeItem>();

        public static List<MenuTreeItem> ParseFromList(List<ITreeNode> menuData, Guid? parentId)
        {
            var result = new List<MenuTreeItem>();

            var items = menuData.Where(x => x.ParentId == parentId)
                               .OrderBy(x => x.DisplayOrder)
                               .ToList();

            foreach (var item in items)
            {
                var menuItem = new MenuTreeItem
                {
                    Id = item.Id,
                    BusinessPermissionId = item.BusinessPermissionId,
                    ParentId = item.ParentId,
                    Title = item.Title,
                    ActionName = item.ActionName,
                    ControllerName = item.ControllerName,
                    MenuClass = item.MenuClass,
                    IsSection = item.IsSection,
                    DisplayOrder = item.DisplayOrder,
                    Children = ParseFromList(menuData, item.Id)
                };

                result.Add(menuItem);
            }

            return result;
        }
    }
}
'@
    Set-Content -Path $menuTreeItemPath -Value $menuTreeItemContent -Encoding UTF8
    $missingFiles += $menuTreeItemPath
    Write-Host "✓ Created MenuTreeItem.cs" -ForegroundColor Green
}

# Kiểm tra và tạo GetMenuTreeItemQuery.cs
$getMenuTreeItemQueryPath = Join-Path $RootPath "Access\Queries\GetMenuTreeItemQuery.cs"
if (-not (Test-Path $getMenuTreeItemQueryPath)) {
    $getMenuTreeItemQueryContent = @'
using Webaby;

namespace Webaby.Core.Access.Queries
{
    public class GetMenuTreeItemQuery : QueryBase<MenuTreeItem>
    {
        // Empty query - no parameters needed
    }
}
'@
    Set-Content -Path $getMenuTreeItemQueryPath -Value $getMenuTreeItemQueryContent -Encoding UTF8
    $missingFiles += $getMenuTreeItemQueryPath
    Write-Host "✓ Created GetMenuTreeItemQuery.cs" -ForegroundColor Green
}

# Cập nhật .csproj với các file mới tạo
if ($missingFiles.Count -gt 0 -and $PSCmdlet.ShouldProcess($CsProjFile,'Add missing files to .csproj')) {
    try {
        [xml]$proj = Get-Content -Path $CsProjFile
        $ns = $proj.Project.NamespaceURI
        $ig = $proj.Project.ItemGroup | Where-Object { $_.Compile } | Select-Object -First 1
        if (-not $ig) {
            $ig = $proj.CreateElement('ItemGroup',$ns)
            $proj.Project.AppendChild($ig) | Out-Null
        }
        $base = Split-Path -Path $CsProjFile -Parent
        $uri = New-Object System.Uri("$base\")

        foreach ($f in $missingFiles) {
            $rel = $uri.MakeRelativeUri((New-Object System.Uri($f))).ToString() -replace '/', '\'
            if (-not ($ig.Compile | Where-Object { $_.Include -eq $rel })) {
                $node = $proj.CreateElement('Compile',$ns)
                $node.SetAttribute('Include',$rel)
                $ig.AppendChild($node) | Out-Null
                Write-Host "   + Added missing file: $rel" -ForegroundColor Cyan
            }
        }
        $proj.Save($CsProjFile)
    }
    catch {
        Write-Host "✗ Error adding missing files to .csproj: $_" -ForegroundColor Red
    }
}

Write-Host "`n=== Summary ===" -ForegroundColor Magenta
Write-Host "Processed $($modified.Count) QueryHandler files" -ForegroundColor White
Write-Host "Created $($missingFiles.Count) missing files" -ForegroundColor White
Write-Host "Script completed!" -ForegroundColor Green
