# 
# .SYNOPSIS 
#   Refactor QueryHandler → async + ctor + LINQ-to-async for .Where queries + mapping → update .csproj 
# .DESCRIPTION 
#   - Với mỗi class *QueryHandler* trong thư mục Queries của modules: 
#       1) Thêm thiếu usings   
#       2) Chèn constructor   
#       3) LINQ-to-async cho Execute/ExecuteAsync có `EntitySet.Get<T>().Where(...)`   
#       4) Nếu không match bước 3, thực hiện: 
#          a) Đ<PERSON>i signature `Execute` → `async Task<QueryResult<T>> ExecuteAsync(...)`   
#          b) Thay `EntitySet.Get<T>(...)` → `await EntitySet.GetAsync<T>(...)`   
#          c) Mapping động `new QueryResult<T>(X.FromEntity(...))` & `QueryResult.Create(...)`   
#   - Cuối cùng cập nhật `.csproj` với `<Compile Include="…"/>`. 
# 
[CmdletBinding(SupportsShouldProcess=$true)] 
param( 
  [string]   $RootPath   = 'C:\Users\<USER>\source\Workspaces\CEP_NETCORE\Webaby.Core', 
  [string[]] $Modules    = @('Access','BusinessSettings'), 
  [string]   $CsProjFile = 'C:\Users\<USER>\source\Workspaces\CEP_NETCORE\Webaby.Core\Webaby.Core.csproj' 
) 
 
$modified = [System.Collections.Generic.List[string]]::new() 
 
foreach ($mod in $Modules) { 
  $dir = Join-Path (Join-Path $RootPath $mod) 'Queries' 
  if (-not (Test-Path $dir)) { continue } 
 
  Get-ChildItem $dir -Recurse -Filter '*.cs' | ForEach-Object { 
    $file = $_.FullName 
    if (-not (Select-String -Path $file -Pattern 'class\s+\w+QueryHandler' -Quiet)) { return } 
    if (-not $PSCmdlet.ShouldProcess($file,'Refactor')) { return } 
 
    Write-Host "Refactoring: $file" 
    $text = Get-Content -Raw -Path $file 
 
    # 1) Thêm thiếu usings 
    $needed = @( 
      'using System.Threading.Tasks;', 
      'using AutoMapper;', 
      'using Webaby.Data;', 
      'using Webaby.Localization;', 
      'using Webaby;' 
    ) 
    $lines = $text -split "`r?`n" 
    $usings = $lines | Select-String '^using .+;$' 
    if ($usings) { 
      $idx = ($usings | ForEach-Object LineNumber | Measure-Object -Maximum).Maximum - 1 
      foreach ($u in $needed) { 
        if ($text -notmatch [regex]::Escape($u)) { 
          $lines = $lines[0..$idx] + $u + $lines[($idx+1)..($lines.Length-1)] 
          $idx++ 
        } 
      } 
      $text = $lines -join "`r`n" 
    } 
 
    # 2) Chèn constructor 
    $text = $text -replace '(?ms)(internal\s+class\s+(\w+QueryHandler)\s*:\s*[^{]+)\{', @' 
$1 { 
    public $2( 
        IEntitySet entitySet, 
        IRepository repository, 
        IText text, 
        IMapper mapper) 
      : base(entitySet, repository, text, mapper) 
    { } 
'@ 
 
    # 3) LINQ-to-async cho Execute/ExecuteAsync có .Where(...) 
    $linqPat = @' 
(?ms)                                     # multiline, dot matches newline 
^(\s*)public\s+override\s+              # start of method 
   (?: 
     QueryResult<(\w+)>                  # group2 = T for sync Execute 
     \s+Execute 
   | 
     async\s+Task<QueryResult<(\w+)>>    # group3 = T for async ExecuteAsync 
     \s+ExecuteAsync 
   ) 
   \s*\(\s*([^\)]*)\)                    # group4 = parameters 
   \s*\{[\s\S]*?                         # opening brace + any whitespace 
   var\s+(\w+)\s*=\s*                   # group5 = variable name 
   (EntitySet\.Get<[^>]+>\(\)\s*\.Where\([^\)]+\))  # group6 = the full queryable 
   \s*; 
   [\s\S]*?return\s+ 
     (?:QueryResult\.Create\(\5\s*,[^\)]+\)|new\s+QueryResult<(?:\2|\3)>\(\5\)) 
   [\s\S]*?\}                            # up to closing brace 
'@ 
 
    $count = 0 
    $text = [regex]::Replace($text, $linqPat, { 
      param($m) 
      $count++ 
      $T = if ($m.Groups[2].Success) { $m.Groups[2].Value } else { $m.Groups[3].Value } 
      $P = $m.Groups[4].Value.Trim() 
      $V = $m.Groups[5].Value 
      $Q = $m.Groups[6].Value.Trim() 
      @" 
public override async Task<QueryResult<$T>> ExecuteAsync($P) 
{ 
    var queryable = $Q; 
    var entities  = await queryable.ToArrayAsync(); 
    return new QueryResult<$T>(entities); 
} 
"@ 
    }) 
 
    if ($count -eq 0) { 
      # 4a) Đổi signature Execute → async Task<QueryResult<T>> ExecuteAsync 
      $text = $text -replace 'public override\s+QueryResult<(\w+)>\s+Execute', 'public override async Task<QueryResult<$1>> ExecuteAsync' 
 
      # 4b) Thay EntitySet.Get<T>(...) → await EntitySet.GetAsync<T>(...) 
      $text = [regex]::Replace($text, 
        'EntitySet\.Get<([^>]+)>\(\s*([^\)]*)\)', 
        'await EntitySet.GetAsync<$1>($2)' 
      ) 
 
      # 4c) Mapping new QueryResult<T>(X.FromEntity(var)) 
      $text = [regex]::Replace( 
        $text, 
        'new\s+QueryResult<(\w+)>\(\s*\w+\.FromEntity\(\s*(\w+)\s*\)\s*\)', 
        'new QueryResult<$1>(Mapper.Map<$1>($2))' 
      ) 
 
      # 4d) Mapping QueryResult.Create(entity, X.FromEntity) 
      $text = [regex]::Replace( 
        $text, 
        'QueryResult\.Create\(\s*(\w+)\s*,\s*(\w+)\.FromEntity', 
        'QueryResult.Create($1, x => Mapper.Map<$2>(x))' 
      ) 
    } 
 
    # Lưu file 
    Set-Content -Path $file -Value $text -Encoding UTF8 
    $modified.Add($file) 
  } 
} 
 
# 5) Cập nhật .csproj 
if ($modified.Count -gt 0 -and $PSCmdlet.ShouldProcess($CsProjFile,'Update .csproj')) { 
  Write-Host "Updating csproj: $CsProjFile" 
  [xml]$proj = Get-Content -Path $CsProjFile 
  $ns   = $proj.Project.NamespaceURI 
  $ig   = $proj.Project.ItemGroup | Where-Object { $_.Compile } | Select-Object -First 1 
  if (-not $ig) { 
    $ig = $proj.CreateElement('ItemGroup',$ns) 
    $proj.Project.AppendChild($ig) | Out-Null 
  } 
  $base = Split-Path -Path $CsProjFile -Parent 
  $uri  = New-Object System.Uri("$base\") 
  foreach ($f in $modified) { 
    $rel = $uri.MakeRelativeUri((New-Object System.Uri($f))).ToString() -replace '/', '\' 
    if (-not ($ig.Compile | Where-Object { $_.Include -eq $rel })) { 
      if ($PSCmdlet.ShouldProcess($rel,'Add Compile Include')) { 
        $node = $proj.CreateElement('Compile',$ns) 
        $node.SetAttribute('Include',$rel) 
        $ig.AppendChild($node) | Out-Null 
        Write-Host "   + Added Compile Include='$rel'" 
      } 
    } 
  } 
  $proj.Save($CsProjFile) 
  Write-Host 'Done.' 
} 
else { 
  Write-Host 'No changes.' 
}
