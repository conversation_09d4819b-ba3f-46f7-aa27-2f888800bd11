# 
# .SYNOPSIS 
#   Master script để thực hiện toàn bộ quá trình refactor QueryHandler
# .DESCRIPTION 
#   Script này sẽ:
#   1. Tạo các Entity classes còn thiếu
#   2. Refactor tất cả QueryHandler files
#   3. Build project để kiểm tra lỗi
#   4. Hi<PERSON>n thị báo cáo tổng kết
# 
[CmdletBinding(SupportsShouldProcess=$true)] 
param( 
  [string] $RootPath = 'C:\Users\<USER>\source\Workspaces\CEP_NETCORE\Webaby.Core',
  [string[]] $Modules = @('Access','BusinessSettings'),
  [switch] $SkipBuild = $false
) 

$ErrorActionPreference = 'Continue'

Write-Host "=== WEBABY CORE QUERYHANDLER REFACTORING ===" -ForegroundColor Magenta
Write-Host "Root Path: $RootPath" -ForegroundColor White
Write-Host "Modules: $($Modules -join ', ')" -ForegroundColor White
Write-Host "Skip Build: $SkipBuild" -ForegroundColor White
Write-Host ""

# Bước 1: Tạo các Entity classes còn thiếu
Write-Host "STEP 1: Creating missing Entity classes..." -ForegroundColor Yellow
try {
    & ".\create-missing-entities.ps1" -RootPath $RootPath
    Write-Host "✓ Entity creation completed" -ForegroundColor Green
}
catch {
    Write-Host "✗ Error in entity creation: $_" -ForegroundColor Red
}
Write-Host ""

# Bước 2: Refactor QueryHandler files
Write-Host "STEP 2: Refactoring QueryHandler files..." -ForegroundColor Yellow
try {
    & ".\refactor-queryhandlers-improved.ps1" -RootPath $RootPath -Modules $Modules
    Write-Host "✓ QueryHandler refactoring completed" -ForegroundColor Green
}
catch {
    Write-Host "✗ Error in QueryHandler refactoring: $_" -ForegroundColor Red
}
Write-Host ""

# Bước 3: Build project để kiểm tra lỗi
if (-not $SkipBuild) {
    Write-Host "STEP 3: Building project to check for errors..." -ForegroundColor Yellow
    $csprojPath = Join-Path $RootPath "Webaby.Core.csproj"
    
    if (Test-Path $csprojPath) {
        try {
            $buildResult = dotnet build $csprojPath 2>&1
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✓ Build successful!" -ForegroundColor Green
            } else {
                Write-Host "✗ Build failed. Errors:" -ForegroundColor Red
                $buildResult | Where-Object { $_ -match "error" } | ForEach-Object {
                    Write-Host "  $_" -ForegroundColor Red
                }
            }
        }
        catch {
            Write-Host "✗ Error during build: $_" -ForegroundColor Red
        }
    } else {
        Write-Host "✗ Project file not found: $csprojPath" -ForegroundColor Red
    }
} else {
    Write-Host "STEP 3: Skipping build as requested" -ForegroundColor Yellow
}
Write-Host ""

# Bước 4: Báo cáo tổng kết
Write-Host "STEP 4: Final report..." -ForegroundColor Yellow

# Đếm số file QueryHandler đã được refactor
$totalQueryHandlers = 0
foreach ($mod in $Modules) {
    $queriesDir = Join-Path (Join-Path $RootPath $mod) "Queries"
    if (Test-Path $queriesDir) {
        $queryHandlerFiles = Get-ChildItem $queriesDir -Recurse -Filter "*.cs" | Where-Object {
            (Get-Content $_.FullName -Raw) -match 'class\s+\w+QueryHandler'
        }
        $totalQueryHandlers += $queryHandlerFiles.Count
        Write-Host "  $mod module: $($queryHandlerFiles.Count) QueryHandler files" -ForegroundColor Cyan
    }
}

Write-Host ""
Write-Host "=== REFACTORING SUMMARY ===" -ForegroundColor Magenta
Write-Host "✓ Total QueryHandler files processed: $totalQueryHandlers" -ForegroundColor White
Write-Host "✓ All files updated with:" -ForegroundColor White
Write-Host "  - Async/await pattern" -ForegroundColor Gray
Write-Host "  - Constructor dependency injection" -ForegroundColor Gray
Write-Host "  - AutoMapper integration" -ForegroundColor Gray
Write-Host "  - EntityFramework Core async methods" -ForegroundColor Gray
Write-Host "  - Updated .csproj references" -ForegroundColor Gray
Write-Host ""

if (-not $SkipBuild -and $LASTEXITCODE -eq 0) {
    Write-Host "🎉 REFACTORING COMPLETED SUCCESSFULLY! 🎉" -ForegroundColor Green
} elseif ($SkipBuild) {
    Write-Host "⚠️  REFACTORING COMPLETED (Build skipped)" -ForegroundColor Yellow
} else {
    Write-Host "⚠️  REFACTORING COMPLETED WITH BUILD ERRORS" -ForegroundColor Yellow
    Write-Host "Please review the build errors above and fix them manually." -ForegroundColor White
}

Write-Host ""
Write-Host "Next steps:" -ForegroundColor White
Write-Host "1. Review the refactored QueryHandler files" -ForegroundColor Gray
Write-Host "2. Test the application to ensure everything works" -ForegroundColor Gray
Write-Host "3. Commit changes to source control" -ForegroundColor Gray
